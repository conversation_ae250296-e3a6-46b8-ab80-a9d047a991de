'use client';

import React from 'react';
import { BarChart3 } from 'lucide-react';

// Mock数据 - 流量统计
const mockTrafficData = {
  monthlyTrend: [
    { month: '1月', visits: 2000 },
    { month: '2月', visits: 3500 },
    { month: '3月', visits: 2800 },
    { month: '4月', visits: 4200 },
    { month: '5月', visits: 5800 },
    { month: '6月', visits: 6000 },
  ],
  websiteStats: {
    visits: '11K',
    bounceRate: '29.31%',
    pageViews: '9.46',
    avgDuration: '00:02:32',
    globalRank: '5,314,280'
  },
  topCountries: [
    { name: '美国', percent: 45, color: 'bg-blue-500' },
    { name: '中国', percent: 35, color: 'bg-green-500' },
    { name: '其他', percent: 20, color: 'bg-gray-400' }
  ],
  trafficSources: [
    { name: '直接访问', percent: 60, color: 'bg-blue-600' },
    { name: '搜索引擎', percent: 25, color: 'bg-green-500' },
    { name: '社交媒体', percent: 10, color: 'bg-purple-500' },
    { name: '外部引用', percent: 5, color: 'bg-orange-500' }
  ],
  keywordValue: [
    { keyword: 'AI工具', value: 85, color: 'bg-blue-500' },
    { keyword: '人工智能', value: 70, color: 'bg-green-500' },
    { keyword: '机器学习', value: 60, color: 'bg-purple-500' },
    { keyword: '深度学习', value: 45, color: 'bg-orange-500' },
    { keyword: '自然语言', value: 30, color: 'bg-pink-500' }
  ]
};

// 进度条组件
function ProgressBar({ percent, color, height = 'h-2' }: { percent: number, color: string, height?: string }) {
  const getBackgroundColor = (colorClass: string) => {
    const colorMap: { [key: string]: string } = {
      'bg-blue-500': 'linear-gradient(90deg, #3b82f6 0%, #60a5fa 100%)',
      'bg-blue-600': 'linear-gradient(90deg, #2563eb 0%, #3b82f6 100%)',
      'bg-green-500': 'linear-gradient(90deg, #10b981 0%, #34d399 100%)',
      'bg-purple-500': 'linear-gradient(90deg, #8b5cf6 0%, #a78bfa 100%)',
      'bg-orange-500': 'linear-gradient(90deg, #f97316 0%, #fb923c 100%)',
      'bg-pink-500': 'linear-gradient(90deg, #ec4899 0%, #f472b6 100%)',
      'bg-yellow-500': 'linear-gradient(90deg, #eab308 0%, #facc15 100%)',
      'bg-indigo-500': 'linear-gradient(90deg, #6366f1 0%, #818cf8 100%)',
      'bg-red-500': 'linear-gradient(90deg, #ef4444 0%, #f87171 100%)',
      'bg-gray-400': 'linear-gradient(90deg, #9ca3af 0%, #d1d5db 100%)'
    };
    return colorMap[colorClass] || '#6b7280';
  };

  return (
    <div className={`w-full bg-gray-700 rounded-full ${height} overflow-hidden relative`}>
      <div
        className={`${height} rounded-full transition-all duration-700 ease-out relative overflow-hidden`}
        style={{
          width: `${percent}%`,
          background: getBackgroundColor(color)
        }}
      >
        {/* 光泽效果 */}
        <div
          className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-20 animate-shimmer"
        />
      </div>
    </div>
  );
}

// 圆环图组件
function DonutChart({ data, size = 120 }: { data: Array<{name: string, percent: number, color: string}>, size?: number }) {
  let cumulativePercent = 0;
  const radius = size / 2 - 12;
  const circumference = 2 * Math.PI * radius;

  // 颜色映射
  const getStrokeColor = (colorClass: string) => {
    const colorMap: { [key: string]: string } = {
      'bg-blue-500': '#3b82f6',
      'bg-green-500': '#10b981',
      'bg-gray-400': '#9ca3af',
      'bg-purple-500': '#8b5cf6',
      'bg-orange-500': '#f97316',
      'bg-pink-500': '#ec4899',
      'bg-yellow-500': '#eab308',
      'bg-indigo-500': '#6366f1',
      'bg-red-500': '#ef4444'
    };
    return colorMap[colorClass] || '#6b7280';
  };

  return (
    <div className="relative flex items-center justify-center" style={{ width: size, height: size }}>
      <svg width={size} height={size} className="transform -rotate-90">
        <defs>
          {data.map((_, index) => (
            <filter key={index} id={`shadow-${index}`}>
              <feDropShadow dx="0" dy="2" stdDeviation="2" floodOpacity="0.3"/>
            </filter>
          ))}
        </defs>

        {/* 背景圆环 */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          fill="none"
          stroke="rgb(55, 65, 81)"
          strokeWidth="10"
        />

        {/* 数据圆环 */}
        {data.map((item, index) => {
          const strokeDasharray = `${(item.percent / 100) * circumference} ${circumference}`;
          const strokeDashoffset = -cumulativePercent * circumference / 100;
          cumulativePercent += item.percent;

          return (
            <circle
              key={index}
              cx={size / 2}
              cy={size / 2}
              r={radius}
              fill="none"
              stroke={getStrokeColor(item.color)}
              strokeWidth="10"
              strokeDasharray={strokeDasharray}
              strokeDashoffset={strokeDashoffset}
              strokeLinecap="round"
              filter={`url(#shadow-${index})`}
              className="transition-all duration-700 ease-out hover:stroke-width-12 cursor-pointer"
              style={{
                transition: 'stroke-dasharray 0.7s ease-in-out, stroke-width 0.2s ease-in-out',
                transformOrigin: 'center'
              }}
            />
          );
        })}
      </svg>

      {/* 中心文字 */}
      <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
        <div className="text-center">
          <div className="text-lg font-bold text-white">
            {data.reduce((sum, item) => sum + item.percent, 0)}%
          </div>
          <div className="text-xs text-gray-400">总计</div>
        </div>
      </div>
    </div>
  );
}

// 面积图组件
function AreaChart({ data }: { data: Array<{month: string, visits: number}> }) {
  const maxVisits = Math.max(...data.map(d => d.visits));
  const minVisits = Math.min(...data.map(d => d.visits));
  const chartHeight = 160;
  const chartWidth = 100; // 使用百分比宽度
  const padding = 20;

  // 生成路径点 - 修复路径生成逻辑
  const pathPoints = data.map((item, index) => {
    const x = (index / (data.length - 1)) * (chartWidth - padding * 2) + padding;
    const y = chartHeight - padding - ((item.visits - minVisits) / (maxVisits - minVisits)) * (chartHeight - padding * 2);
    return { x, y, visits: item.visits };
  });

  // 生成平滑的路径
  const linePath = pathPoints.map((point, index) => {
    if (index === 0) return `M ${point.x} ${point.y}`;
    const prevPoint = pathPoints[index - 1];
    const cpx1 = prevPoint.x + (point.x - prevPoint.x) * 0.5;
    const cpx2 = point.x - (point.x - prevPoint.x) * 0.5;
    return `C ${cpx1} ${prevPoint.y} ${cpx2} ${point.y} ${point.x} ${point.y}`;
  }).join(' ');

  // 生成面积路径
  const areaPath = `${linePath} L ${pathPoints[pathPoints.length - 1].x} ${chartHeight - padding} L ${pathPoints[0].x} ${chartHeight - padding} Z`;

  // Y轴刻度
  const yTicks = [maxVisits, maxVisits * 0.5, 0];

  return (
    <div className="relative w-full h-48 bg-gray-900 rounded-lg p-4 overflow-hidden">
      <svg
        width="100%"
        height="100%"
        viewBox={`0 0 ${chartWidth} ${chartHeight}`}
        className="overflow-visible"
        preserveAspectRatio="none"
      >
        <defs>
          <linearGradient id="areaGradient" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stopColor="rgb(59, 130, 246)" stopOpacity="0.6" />
            <stop offset="100%" stopColor="rgb(59, 130, 246)" stopOpacity="0.1" />
          </linearGradient>
          <filter id="glow">
            <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
            <feMerge>
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
        </defs>

        {/* 网格线 */}
        {yTicks.map((_, index) => {
          const y = padding + (index / (yTicks.length - 1)) * (chartHeight - padding * 2);
          return (
            <line
              key={index}
              x1={padding}
              y1={y}
              x2={chartWidth - padding}
              y2={y}
              stroke="rgb(75, 85, 99)"
              strokeWidth="0.5"
              strokeDasharray="2,2"
              opacity="0.3"
            />
          );
        })}

        {/* 面积 */}
        <path
          d={areaPath}
          fill="url(#areaGradient)"
          className="transition-all duration-1000 ease-out"
        />

        {/* 线条 */}
        <path
          d={linePath}
          fill="none"
          stroke="rgb(59, 130, 246)"
          strokeWidth="2.5"
          strokeLinecap="round"
          strokeLinejoin="round"
          filter="url(#glow)"
          className="transition-all duration-1000 ease-out"
        />

        {/* 数据点 */}
        {pathPoints.map((point, index) => (
          <g key={index}>
            <circle
              cx={point.x}
              cy={point.y}
              r="4"
              fill="rgb(59, 130, 246)"
              stroke="white"
              strokeWidth="2"
              className="transition-all duration-300 ease-out hover:r-6 cursor-pointer"
            />
            {/* 悬停时显示数值 */}
            <text
              x={point.x}
              y={point.y - 10}
              textAnchor="middle"
              fill="white"
              fontSize="10"
              className="opacity-0 hover:opacity-100 transition-opacity duration-200 pointer-events-none"
            >
              {point.visits > 1000 ? `${(point.visits / 1000).toFixed(1)}K` : point.visits}
            </text>
          </g>
        ))}
      </svg>

      {/* Y轴标签 - 修复计算 */}
      <div className="absolute left-0 top-4 h-32 flex flex-col justify-between text-xs text-gray-400 -ml-12">
        <span>{maxVisits > 1000 ? `${(maxVisits / 1000).toFixed(1)}K` : maxVisits}</span>
        <span>{maxVisits > 2000 ? `${(maxVisits / 2000).toFixed(1)}K` : Math.round(maxVisits * 0.5)}</span>
        <span>0</span>
      </div>

      {/* X轴标签 */}
      <div className="absolute bottom-0 left-4 right-4 flex justify-between text-xs text-gray-400 -mb-6">
        {data.map((item, index) => (
          <span key={index} className="text-center">{item.month}</span>
        ))}
      </div>
    </div>
  );
}

export default function TrafficAnalytics({ toolName }: { toolName: string }) {
  return (
    <div className="bg-gray-900 rounded-xl shadow-lg p-6 mb-8 border border-gray-800 animate-fade-in">
      <div className="flex items-center mb-6">
        <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
          <BarChart3 className="w-5 h-5 text-white" />
        </div>
        <div>
          <h2 className="text-xl font-bold text-white">
            {toolName} 网站数据分析
          </h2>
          <p className="text-sm text-gray-400 mt-1">实时流量统计与用户行为分析</p>
        </div>
      </div>

      <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
        {/* 月度流量趋势 */}
        <div className="xl:col-span-2">
          <div className="bg-gray-800 border border-gray-700 rounded-xl p-6 h-full hover:border-gray-600 transition-colors duration-300 animate-scale-in" style={{ animationDelay: '0.1s' }}>
            <div className="flex items-center mb-4">
              <div className="w-8 h-8 bg-blue-600/20 rounded-lg flex items-center justify-center mr-3">
                <BarChart3 className="w-4 h-4 text-blue-400" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-white">月度流量趋势</h3>
                <p className="text-sm text-gray-400">{toolName} 的流量趋势数据</p>
              </div>
            </div>
            <AreaChart data={mockTrafficData.monthlyTrend} />
          </div>
        </div>

        {/* 网站统计数据 */}
        <div className="bg-gray-800 border border-gray-700 rounded-xl p-6 hover:border-gray-600 transition-colors duration-300 animate-scale-in" style={{ animationDelay: '0.2s' }}>
          <div className="flex items-center mb-4">
            <div className="w-8 h-8 bg-green-600/20 rounded-lg flex items-center justify-center mr-3">
              <BarChart3 className="w-4 h-4 text-green-400" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-white">网站统计数据</h3>
              <p className="text-sm text-gray-400">核心指标概览</p>
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex justify-between items-center p-3 bg-gray-700/50 rounded-lg hover:bg-gray-700 transition-colors duration-200">
              <span className="text-sm text-gray-300">访问量</span>
              <span className="text-lg font-bold text-white">{mockTrafficData.websiteStats.visits}</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-gray-700/50 rounded-lg hover:bg-gray-700 transition-colors duration-200">
              <span className="text-sm text-gray-300">跳出率</span>
              <span className="text-lg font-bold text-white">{mockTrafficData.websiteStats.bounceRate}</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-gray-700/50 rounded-lg hover:bg-gray-700 transition-colors duration-200">
              <span className="text-sm text-gray-300">页面访问数</span>
              <span className="text-lg font-bold text-white">{mockTrafficData.websiteStats.pageViews}</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-gray-700/50 rounded-lg hover:bg-gray-700 transition-colors duration-200">
              <span className="text-sm text-gray-300">访问时长</span>
              <span className="text-lg font-bold text-white">{mockTrafficData.websiteStats.avgDuration}</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-gray-700/50 rounded-lg hover:bg-gray-700 transition-colors duration-200">
              <span className="text-sm text-gray-300">全球排名</span>
              <span className="text-lg font-bold text-white">{mockTrafficData.websiteStats.globalRank}</span>
            </div>
          </div>
        </div>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-6">
        {/* 主要访问国家 */}
        <div className="bg-gray-800 border border-gray-700 rounded-xl p-6 hover:border-gray-600 transition-colors duration-300">
          <div className="flex items-center mb-4">
            <div className="w-8 h-8 bg-purple-600/20 rounded-lg flex items-center justify-center mr-3">
              <BarChart3 className="w-4 h-4 text-purple-400" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-white">主要访问国家</h3>
              <p className="text-sm text-gray-400">按地区分布的流量</p>
            </div>
          </div>

          <div className="flex justify-center mb-6">
            <DonutChart data={mockTrafficData.topCountries} size={140} />
          </div>

          <div className="space-y-3">
            {mockTrafficData.topCountries.map((country, index) => (
              <div key={index} className="flex items-center justify-between p-2 rounded-lg hover:bg-gray-700/50 transition-colors duration-200">
                <div className="flex items-center">
                  <div
                    className="w-3 h-3 rounded-full mr-3 shadow-sm"
                    style={{ backgroundColor: country.color.includes('bg-') ?
                      {'bg-blue-500': '#3b82f6', 'bg-green-500': '#10b981', 'bg-gray-400': '#9ca3af'}[country.color] || '#6b7280'
                      : country.color
                    }}
                  />
                  <span className="text-sm text-gray-300 font-medium">{country.name}</span>
                </div>
                <span className="text-sm font-bold text-white">{country.percent}%</span>
              </div>
            ))}
          </div>
        </div>
        
        {/* 流量来源分析 */}
        <div className="bg-gray-800 border border-gray-700 rounded-xl p-6 hover:border-gray-600 transition-colors duration-300">
          <div className="flex items-center mb-4">
            <div className="w-8 h-8 bg-orange-600/20 rounded-lg flex items-center justify-center mr-3">
              <BarChart3 className="w-4 h-4 text-orange-400" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-white">流量来源分析</h3>
              <p className="text-sm text-gray-400">访客来源渠道分布</p>
            </div>
          </div>

          <div className="space-y-4">
            {mockTrafficData.trafficSources.map((source, index) => (
              <div key={index} className="p-3 bg-gray-700/30 rounded-lg hover:bg-gray-700/50 transition-colors duration-200">
                <div className="flex justify-between mb-2">
                  <span className="text-sm text-gray-300 font-medium">{source.name}</span>
                  <span className="text-sm font-bold text-white">{source.percent}%</span>
                </div>
                <ProgressBar percent={source.percent} color={source.color} height="h-2.5" />
              </div>
            ))}
          </div>
        </div>

        {/* 月度关键词价值分析 */}
        <div className="bg-gray-800 border border-gray-700 rounded-xl p-6 hover:border-gray-600 transition-colors duration-300">
          <div className="flex items-center mb-4">
            <div className="w-8 h-8 bg-pink-600/20 rounded-lg flex items-center justify-center mr-3">
              <BarChart3 className="w-4 h-4 text-pink-400" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-white">关键词价值分析</h3>
              <p className="text-sm text-gray-400">主要关键词价值分布</p>
            </div>
          </div>

          <div className="space-y-3">
            {mockTrafficData.keywordValue.map((keyword, index) => (
              <div key={index} className="p-3 bg-gray-700/30 rounded-lg hover:bg-gray-700/50 transition-colors duration-200">
                <div className="flex justify-between mb-2">
                  <span className="text-sm text-gray-300 font-medium">{keyword.keyword}</span>
                  <span className="text-sm font-bold text-white">{keyword.value}</span>
                </div>
                <ProgressBar percent={keyword.value} color={keyword.color} height="h-2" />
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
