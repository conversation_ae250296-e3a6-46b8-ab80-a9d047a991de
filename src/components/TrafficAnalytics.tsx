'use client';

import React, { useState, useEffect } from 'react';
import { Bar<PERSON>hart3, Users, Eye, Clock, Globe, MousePointer, Search, Share2, <PERSON><PERSON><PERSON>, LineChart } from 'lucide-react';
import { AnimatedCounter } from './AnimatedCounter';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

// Mock数据 - 采用demo风格
const mockTrafficData = {
  metrics: [
    { 
      label: "访问量", 
      value: 11000, 
      displayValue: "11K", 
      icon: Users, 
      color: "blue", 
      trend: "+12.5%", 
      trendUp: true 
    },
    {
      label: "跳出率",
      value: 29.31,
      displayValue: "29.31%",
      icon: MousePointer,
      color: "green",
      trend: "-5.2%",
      trendUp: false,
    },
    {
      label: "页面访问数",
      value: 9.46,
      displayValue: "9.46",
      icon: Eye,
      color: "purple",
      trend: "+8.1%",
      trendUp: true,
    },
    {
      label: "访问时长",
      value: 152,
      displayValue: "00:02:32",
      icon: Clock,
      color: "orange",
      trend: "+15.3%",
      trendUp: true,
    },
    {
      label: "全球排名",
      value: 5314280,
      displayValue: "5,314,280",
      icon: Globe,
      color: "pink",
      trend: "+2,156",
      trendUp: true,
    },
  ],
  monthlyTrend: [
    { month: "4月", value: 2800 },
    { month: "5月", value: 4200 },
    { month: "6月", value: 6000 },
  ],
  trafficSources: [
    { name: "直接访问", value: 45, color: "#3B82F6", icon: "🔗" },
    { name: "搜索引擎", value: 30, color: "#10B981", icon: "🔍" },
    { name: "外链引用", value: 15, color: "#F59E0B", icon: "📎" },
    { name: "社交媒体", value: 10, color: "#EF4444", icon: "📱" },
  ],
  countryData: [
    { name: "中国", value: 65, color: "#3B82F6", flag: "🇨🇳" },
    { name: "美国", value: 35, color: "#10B981", flag: "🇺🇸" },
  ],
  keywordData: [
    { name: "AI工具", value: 25, color: "#3B82F6" },
    { name: "人工智能", value: 20, color: "#10B981" },
    { name: "机器学习", value: 18, color: "#F59E0B" },
    { name: "深度学习", value: 15, color: "#EF4444" },
    { name: "自然语言", value: 12, color: "#8B5CF6" },
    { name: "数据分析", value: 10, color: "#EC4899" },
  ]
};

export default function TrafficAnalytics({ toolName }: { toolName: string }) {
  const [isLoaded, setIsLoaded] = useState(false)

  useEffect(() => {
    const timer = setTimeout(() => setIsLoaded(true), 300)
    return () => clearTimeout(timer)
  }, [])

  return (
    <div className="bg-gradient-to-br from-slate-950 via-slate-900 to-slate-950 rounded-xl shadow-2xl p-6 mb-8 border border-slate-700/50 backdrop-blur-sm">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
            <BarChart3 className="w-6 h-6 text-white" />
          </div>
          <div>
            <h2 className="text-2xl font-bold bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
              {toolName} 网站数据分析
            </h2>
            <p className="text-sm text-slate-400">实时流量统计与用户行为分析</p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <Badge variant="secondary" className="bg-green-500/20 text-green-400 border-green-500/30">
            <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse" />
            实时更新
          </Badge>
        </div>
      </div>

      {/* Key Metrics Row */}
      <div className="grid grid-cols-5 gap-4 mb-6">
        {mockTrafficData.metrics.map((metric, index) => {
          const Icon = metric.icon
          return (
            <Card
              key={metric.label}
              className="bg-gradient-to-br from-slate-800/80 to-slate-900/80 border-slate-700/50 backdrop-blur-sm hover:from-slate-800/90 hover:to-slate-900/90 transition-all duration-300 hover:scale-105 hover:shadow-2xl hover:shadow-blue-500/10"
            >
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className={`p-2 rounded-xl bg-${metric.color}-500/20 backdrop-blur-sm`}>
                    <Icon className="w-4 h-4 text-white" />
                  </div>
                  <Badge
                    variant="secondary"
                    className={`text-xs font-semibold ${
                      metric.trendUp
                        ? "bg-green-500/20 text-green-400 border-green-500/30"
                        : "bg-red-500/20 text-red-400 border-red-500/30"
                    }`}
                  >
                    {metric.trend}
                  </Badge>
                </div>
                <div className="space-y-1">
                  <p className="text-xl font-bold text-white">
                    {typeof metric.value === "number" &&
                    metric.label !== "跳出率" &&
                    metric.label !== "页面访问数" ? (
                      <AnimatedCounter end={metric.value} />
                    ) : (
                      metric.displayValue
                    )}
                  </p>
                  <p className="text-xs text-slate-400 font-medium">{metric.label}</p>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Charts Row 1 */}
      <div className="grid grid-cols-3 gap-6 h-80 mb-6">
        {/* Monthly Traffic Trend */}
        <Card className="col-span-2 bg-gradient-to-br from-slate-800/80 to-slate-900/80 border-slate-700/50 backdrop-blur-sm hover:shadow-2xl hover:shadow-blue-500/10 transition-all duration-300">
          <CardHeader className="pb-4">
            <CardTitle className="text-white flex items-center gap-3 text-lg">
              <div className="p-2 bg-blue-500/20 rounded-lg">
                <LineChart className="w-5 h-5 text-blue-400" />
              </div>
              月度流量趋势
              <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse ml-auto" />
            </CardTitle>
            <p className="text-slate-400 text-sm">{toolName} 的流量趋势数据</p>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="h-48">
              <svg viewBox="0 0 400 180" className="w-full h-full">
                <defs>
                  <linearGradient id="areaGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                    <stop offset="0%" stopColor="#3B82F6" stopOpacity="0.8" />
                    <stop offset="50%" stopColor="#1E40AF" stopOpacity="0.4" />
                    <stop offset="100%" stopColor="#1E3A8A" stopOpacity="0.1" />
                  </linearGradient>
                  <filter id="glow">
                    <feGaussianBlur stdDeviation="3" result="coloredBlur" />
                    <feMerge>
                      <feMergeNode in="coloredBlur" />
                      <feMergeNode in="SourceGraphic" />
                    </feMerge>
                  </filter>
                </defs>

                {/* Grid */}
                {[0, 2000, 4000, 6000].map((value) => (
                  <g key={value}>
                    <line
                      x1="60"
                      y1={140 - (value / 6000) * 100}
                      x2="340"
                      y2={140 - (value / 6000) * 100}
                      stroke="#374151"
                      strokeWidth="1"
                      opacity="0.4"
                      strokeDasharray="2,2"
                    />
                    <text
                      x="50"
                      y={145 - (value / 6000) * 100}
                      fill="#9CA3AF"
                      fontSize="10"
                      textAnchor="end"
                      className="font-medium"
                    >
                      {value === 0 ? "0" : `${value / 1000}K`}
                    </text>
                  </g>
                ))}

                {/* Area */}
                <path
                  d="M 60 140 L 60 90 Q 200 50 340 40 L 340 140 Z"
                  fill="url(#areaGradient)"
                  className={`transition-all duration-2000 ${isLoaded ? "opacity-100" : "opacity-0"}`}
                />

                {/* Line */}
                <path
                  d="M 60 90 Q 200 50 340 40"
                  stroke="#3B82F6"
                  strokeWidth="3"
                  fill="none"
                  filter="url(#glow)"
                  className={`transition-all duration-2000 ${isLoaded ? "opacity-100" : "opacity-0"}`}
                />

                {/* Points */}
                {mockTrafficData.monthlyTrend.map((data, index) => (
                  <g key={data.month}>
                    <circle
                      cx={60 + index * 140}
                      cy={140 - (data.value / 6000) * 100}
                      r="4"
                      fill="#1E40AF"
                      className={`transition-all duration-1000 hover:r-6 cursor-pointer ${isLoaded ? "opacity-100" : "opacity-0"}`}
                      style={{ animationDelay: `${index * 300}ms` }}
                    />
                    <circle
                      cx={60 + index * 140}
                      cy={140 - (data.value / 6000) * 100}
                      r="2"
                      fill="#3B82F6"
                      className={`transition-all duration-1000 ${isLoaded ? "opacity-100" : "opacity-0"}`}
                      style={{ animationDelay: `${index * 300}ms` }}
                    />
                  </g>
                ))}

                {/* Labels */}
                {mockTrafficData.monthlyTrend.map((data, index) => (
                  <text
                    key={data.month}
                    x={60 + index * 140}
                    y="160"
                    fill="#9CA3AF"
                    fontSize="12"
                    textAnchor="middle"
                    className="font-medium"
                  >
                    {data.month}
                  </text>
                ))}
              </svg>
            </div>
          </CardContent>
        </Card>

        {/* Network Statistics */}
        <Card className="bg-gradient-to-br from-slate-800/80 to-slate-900/80 border-slate-700/50 backdrop-blur-sm hover:shadow-2xl hover:shadow-purple-500/10 transition-all duration-300">
          <CardHeader className="pb-4">
            <CardTitle className="text-white flex items-center gap-3 text-lg">
              <div className="p-2 bg-purple-500/20 rounded-lg">
                <BarChart3 className="w-5 h-5 text-purple-400" />
              </div>
              网站统计数据
            </CardTitle>
            <p className="text-slate-400 text-sm">网站核心指标统计数据概览</p>
          </CardHeader>
          <CardContent className="pt-0 space-y-3">
            {mockTrafficData.metrics.slice(0, 4).map((metric, index) => (
              <div
                key={metric.label}
                className="flex items-center justify-between p-3 rounded-xl bg-gradient-to-r from-slate-700/30 to-slate-600/20 hover:from-slate-700/50 hover:to-slate-600/30 transition-all duration-300 border border-slate-600/30 hover:border-slate-500/50"
              >
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse" />
                  <span className="text-slate-300 font-medium text-sm">{metric.label}</span>
                </div>
                <span className="text-white font-bold text-sm">{metric.displayValue}</span>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>

      {/* Charts Row 2 */}
      <div className="grid grid-cols-3 gap-6 h-80">
        {/* Country Distribution */}
        <Card className="bg-gradient-to-br from-slate-800/80 to-slate-900/80 border-slate-700/50 backdrop-blur-sm hover:shadow-2xl hover:shadow-green-500/10 transition-all duration-300">
          <CardHeader className="pb-4">
            <CardTitle className="text-white flex items-center gap-3 text-lg">
              <div className="p-2 bg-green-500/20 rounded-lg">
                <PieChart className="w-5 h-5 text-green-400" />
              </div>
              主要访问国家
            </CardTitle>
            <p className="text-slate-400 text-sm">按国家/地区分布的流量</p>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="flex items-center justify-center h-32 mb-4">
              <div className="relative w-28 h-28">
                <svg viewBox="0 0 100 100" className="w-full h-full transform -rotate-90">
                  <circle
                    cx="50"
                    cy="50"
                    r="35"
                    fill="none"
                    stroke="#10B981"
                    strokeWidth="12"
                    strokeDasharray={`${mockTrafficData.countryData[1].value * 2.2} ${(100 - mockTrafficData.countryData[1].value) * 2.2}`}
                    className="transition-all duration-2000 drop-shadow-lg"
                    filter="url(#glow)"
                  />
                  <circle
                    cx="50"
                    cy="50"
                    r="35"
                    fill="none"
                    stroke="#3B82F6"
                    strokeWidth="12"
                    strokeDasharray={`${mockTrafficData.countryData[0].value * 2.2} ${(100 - mockTrafficData.countryData[0].value) * 2.2}`}
                    strokeDashoffset={`${-mockTrafficData.countryData[1].value * 2.2}`}
                    className="transition-all duration-2000 drop-shadow-lg"
                    filter="url(#glow)"
                  />
                </svg>
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center">
                    <div className="text-lg font-bold text-white">100%</div>
                    <div className="text-xs text-slate-400">总访问</div>
                  </div>
                </div>
              </div>
            </div>
            <div className="space-y-2">
              {mockTrafficData.countryData.map((country) => (
                <div
                  key={country.name}
                  className="flex items-center justify-between p-3 rounded-xl bg-slate-700/30 hover:bg-slate-700/50 transition-colors"
                >
                  <div className="flex items-center gap-3">
                    <span className="text-lg">{country.flag}</span>
                    <div className="w-3 h-3 rounded-full" style={{ backgroundColor: country.color }} />
                    <span className="text-slate-300 font-medium text-sm">{country.name}</span>
                  </div>
                  <span className="text-white font-bold text-sm">{country.value}%</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Traffic Sources */}
        <Card className="bg-gradient-to-br from-slate-800/80 to-slate-900/80 border-slate-700/50 backdrop-blur-sm hover:shadow-2xl hover:shadow-orange-500/10 transition-all duration-300">
          <CardHeader className="pb-4">
            <CardTitle className="text-white flex items-center gap-3 text-lg">
              <div className="p-2 bg-orange-500/20 rounded-lg">
                <Search className="w-5 h-5 text-orange-400" />
              </div>
              流量来源分析
            </CardTitle>
            <p className="text-slate-400 text-sm">访客来源渠道分布</p>
          </CardHeader>
          <CardContent className="pt-0 space-y-4">
            {mockTrafficData.trafficSources.map((source, index) => (
              <div key={source.name} className="space-y-2">
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-3">
                    <span className="text-sm">{source.icon}</span>
                    <span className="text-slate-300 font-medium text-sm">{source.name}</span>
                  </div>
                  <span className="text-white font-bold text-sm">{source.value}%</span>
                </div>
                <div className="relative w-full bg-slate-700/50 rounded-full h-2 overflow-hidden">
                  <div
                    className="h-full rounded-full transition-all duration-2000 ease-out relative"
                    style={{
                      width: isLoaded ? `${source.value}%` : "0%",
                      backgroundColor: source.color,
                      transitionDelay: `${index * 300}ms`,
                      boxShadow: `0 0 10px ${source.color}40`,
                    }}
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse" />
                  </div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Keywords Analysis */}
        <Card className="bg-gradient-to-br from-slate-800/80 to-slate-900/80 border-slate-700/50 backdrop-blur-sm hover:shadow-2xl hover:shadow-pink-500/10 transition-all duration-300">
          <CardHeader className="pb-4">
            <CardTitle className="text-white flex items-center gap-3 text-lg">
              <div className="p-2 bg-pink-500/20 rounded-lg">
                <Share2 className="w-5 h-5 text-pink-400" />
              </div>
              关键词价值分析
            </CardTitle>
            <p className="text-slate-400 text-sm">主要关键词价值分布</p>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="flex items-center justify-center h-28 mb-4">
              <div className="relative w-24 h-24">
                <svg viewBox="0 0 100 100" className="w-full h-full transform -rotate-90">
                  {mockTrafficData.keywordData.map((keyword, index) => {
                    const offset = mockTrafficData.keywordData.slice(0, index).reduce((sum, item) => sum + item.value, 0) * 2.2
                    return (
                      <circle
                        key={keyword.name}
                        cx="50"
                        cy="50"
                        r="30"
                        fill="none"
                        stroke={keyword.color}
                        strokeWidth="8"
                        strokeDasharray={`${keyword.value * 2.2} ${(100 - keyword.value) * 2.2}`}
                        strokeDashoffset={`-${offset}`}
                        className="transition-all duration-2000 hover:stroke-width-10 cursor-pointer"
                        style={{
                          transitionDelay: `${index * 200}ms`,
                          filter: `drop-shadow(0 0 5px ${keyword.color}60)`,
                        }}
                      />
                    )
                  })}
                </svg>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-2">
              {mockTrafficData.keywordData.map((keyword, index) => (
                <div
                  key={keyword.name}
                  className="flex items-center gap-2 p-2 rounded-lg bg-slate-700/30 hover:bg-slate-700/50 transition-colors cursor-pointer"
                >
                  <div className="w-2 h-2 rounded-full animate-pulse" style={{ backgroundColor: keyword.color }} />
                  <span className="text-slate-300 text-xs font-medium">{keyword.name}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
