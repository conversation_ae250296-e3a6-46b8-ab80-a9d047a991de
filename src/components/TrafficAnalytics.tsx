'use client';

import React from 'react';
import { BarChart3 } from 'lucide-react';

// Mock数据 - 流量统计
const mockTrafficData = {
  monthlyTrend: [
    { month: '4月', visits: 1000 },
    { month: '5月', visits: 4000 },
    { month: '6月', visits: 6000 },
  ],
  websiteStats: {
    visits: '11K',
    bounceRate: '29.31%',
    pageViews: '9.46',
    avgDuration: '00:02:32',
    globalRank: '5,314,280'
  },
  topCountries: [
    { name: '美国', percent: 45, color: 'bg-blue-500' },
    { name: '中国', percent: 35, color: 'bg-green-500' },
    { name: '其他', percent: 20, color: 'bg-gray-400' }
  ],
  trafficSources: [
    { name: '直接访问', percent: 60, color: 'bg-blue-600' },
    { name: '搜索引擎', percent: 25, color: 'bg-green-500' },
    { name: '社交媒体', percent: 10, color: 'bg-purple-500' },
    { name: '外部引用', percent: 5, color: 'bg-orange-500' }
  ],
  keywordValue: [
    { keyword: 'AI工具', value: 85, color: 'bg-blue-500' },
    { keyword: '人工智能', value: 70, color: 'bg-green-500' },
    { keyword: '机器学习', value: 60, color: 'bg-purple-500' },
    { keyword: '深度学习', value: 45, color: 'bg-orange-500' },
    { keyword: '自然语言', value: 30, color: 'bg-pink-500' }
  ]
};

// 进度条组件
function ProgressBar({ percent, color, height = 'h-2' }: { percent: number, color: string, height?: string }) {
  return (
    <div className={`w-full bg-gray-200 dark:bg-gray-700 rounded-full ${height}`}>
      <div
        className={`${height} rounded-full ${color} transition-all duration-500 ease-out`}
        style={{ width: `${percent}%` }}
      />
    </div>
  );
}

// 圆环图组件
function DonutChart({ data, size = 120 }: { data: Array<{name: string, percent: number, color: string}>, size?: number }) {
  let cumulativePercent = 0;
  const radius = size / 2 - 10;
  const circumference = 2 * Math.PI * radius;

  return (
    <div className="relative" style={{ width: size, height: size }}>
      <svg width={size} height={size} className="transform -rotate-90">
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          fill="none"
          stroke="currentColor"
          strokeWidth="8"
          className="text-gray-700"
        />
        {data.map((item, index) => {
          const strokeDasharray = `${(item.percent / 100) * circumference} ${circumference}`;
          const strokeDashoffset = -cumulativePercent * circumference / 100;
          cumulativePercent += item.percent;
          
          return (
            <circle
              key={index}
              cx={size / 2}
              cy={size / 2}
              r={radius}
              fill="none"
              strokeWidth="8"
              strokeDasharray={strokeDasharray}
              strokeDashoffset={strokeDashoffset}
              className={item.color.replace('bg-', 'stroke-')}
              style={{ transition: 'stroke-dasharray 0.5s ease-in-out' }}
            />
          );
        })}
      </svg>
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="text-center">
          <div className="text-lg font-bold text-white">100%</div>
          <div className="text-xs text-gray-400">总计</div>
        </div>
      </div>
    </div>
  );
}

// 面积图组件
function AreaChart({ data }: { data: Array<{month: string, visits: number}> }) {
  const maxVisits = Math.max(...data.map(d => d.visits));
  const chartHeight = 200;
  const chartWidth = 400;
  
  // 生成路径点
  const points = data.map((item, index) => {
    const x = (index / (data.length - 1)) * chartWidth;
    const y = chartHeight - (item.visits / maxVisits) * chartHeight;
    return `${x},${y}`;
  }).join(' ');
  
  // 生成面积路径
  const areaPath = `M 0,${chartHeight} L ${points} L ${chartWidth},${chartHeight} Z`;
  
  return (
    <div className="relative w-full h-48 bg-gray-900 rounded-lg p-4">
      <svg width="100%" height="100%" viewBox={`0 0 ${chartWidth} ${chartHeight}`} className="overflow-visible">
        <defs>
          <linearGradient id="areaGradient" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stopColor="rgb(59, 130, 246)" stopOpacity="0.8" />
            <stop offset="100%" stopColor="rgb(59, 130, 246)" stopOpacity="0.1" />
          </linearGradient>
        </defs>
        <path
          d={areaPath}
          fill="url(#areaGradient)"
          className="transition-all duration-1000 ease-out"
        />
        <polyline
          points={points}
          fill="none"
          stroke="rgb(59, 130, 246)"
          strokeWidth="3"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="transition-all duration-1000 ease-out"
        />
        {data.map((item, index) => {
          const x = (index / (data.length - 1)) * chartWidth;
          const y = chartHeight - (item.visits / maxVisits) * chartHeight;
          return (
            <circle
              key={index}
              cx={x}
              cy={y}
              r="4"
              fill="rgb(59, 130, 246)"
              className="transition-all duration-1000 ease-out"
            />
          );
        })}
      </svg>
      
      {/* Y轴标签 */}
      <div className="absolute left-0 top-0 h-full flex flex-col justify-between text-xs text-gray-400 -ml-8">
        <span>{(maxVisits / 1000).toFixed(1)}K</span>
        <span>{(maxVisits / 2000).toFixed(1)}K</span>
        <span>0</span>
      </div>

      {/* X轴标签 */}
      <div className="absolute bottom-0 left-0 w-full flex justify-between text-xs text-gray-400 -mb-6">
        {data.map((item, index) => (
          <span key={index}>{item.month}</span>
        ))}
      </div>
    </div>
  );
}

export default function TrafficAnalytics({ toolName }: { toolName: string }) {
  return (
    <div className="bg-gray-900 rounded-lg shadow-sm p-6 mb-8">
      <div className="flex items-center mb-6">
        <BarChart3 className="w-6 h-6 text-white mr-2" />
        <h2 className="text-xl font-bold text-white">
          {toolName} 网站数据分析
        </h2>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 月度流量趋势 */}
        <div className="lg:col-span-2">
          <div className="bg-gray-800 border border-gray-700 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-white mb-2">月度流量趋势</h3>
            <p className="text-sm text-gray-400 mb-4">{toolName} 的流量趋势数据</p>
            <AreaChart data={mockTrafficData.monthlyTrend} />
          </div>
        </div>
        
        {/* 网站统计数据 */}
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-white mb-4">网站统计数据</h3>
          <p className="text-sm text-gray-400 mb-4">网站核心指标统计数据概览</p>

          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-400">访问量</span>
              <span className="text-lg font-bold text-white">{mockTrafficData.websiteStats.visits}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-400">跳出率</span>
              <span className="text-lg font-bold text-white">{mockTrafficData.websiteStats.bounceRate}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-400">页面访问数</span>
              <span className="text-lg font-bold text-white">{mockTrafficData.websiteStats.pageViews}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-400">访问时长</span>
              <span className="text-lg font-bold text-white">{mockTrafficData.websiteStats.avgDuration}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-400">全球排名</span>
              <span className="text-lg font-bold text-white">{mockTrafficData.websiteStats.globalRank}</span>
            </div>
          </div>
        </div>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-6">
        {/* 主要访问国家 */}
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-white mb-2">主要访问国家</h3>
          <p className="text-sm text-gray-400 mb-4">按国家/地区分布的流量</p>
          
          <div className="flex justify-center mb-4">
            <DonutChart data={mockTrafficData.topCountries} />
          </div>
          
          <div className="space-y-2">
            {mockTrafficData.topCountries.map((country, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className={`w-3 h-3 rounded-full ${country.color} mr-2`} />
                  <span className="text-sm text-gray-300">{country.name}</span>
                </div>
                <span className="text-sm font-medium text-white">{country.percent}%</span>
              </div>
            ))}
          </div>
        </div>
        
        {/* 流量来源分析 */}
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-white mb-2">流量来源分析</h3>
          <p className="text-sm text-gray-400 mb-4">访客来源渠道分布</p>

          <div className="space-y-4">
            {mockTrafficData.trafficSources.map((source, index) => (
              <div key={index}>
                <div className="flex justify-between mb-1">
                  <span className="text-sm text-gray-300">{source.name}</span>
                  <span className="text-sm font-medium text-white">{source.percent}%</span>
                </div>
                <ProgressBar percent={source.percent} color={source.color} />
              </div>
            ))}
          </div>
        </div>
        
        {/* 月度关键词价值分析 */}
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-white mb-2">月度关键词价值分析</h3>
          <p className="text-sm text-gray-400 mb-4">主要关键词价值分布</p>

          <div className="space-y-3">
            {mockTrafficData.keywordValue.map((keyword, index) => (
              <div key={index}>
                <div className="flex justify-between mb-1">
                  <span className="text-sm text-gray-300">{keyword.keyword}</span>
                  <span className="text-sm font-medium text-white">{keyword.value}</span>
                </div>
                <ProgressBar percent={keyword.value} color={keyword.color} height="h-1.5" />
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
