# 流量统计分析组件

## 概述

为工具详情页面添加了流量统计分析功能，参考了 stakpak.dev 网站数据分析的设计风格，提供了丰富的数据可视化展示。

## 功能特性

### 1. 月度流量趋势图
- 使用自定义的面积图组件展示流量趋势
- 支持深色主题设计
- 平滑的动画效果
- 响应式设计

### 2. 网站统计数据
- 访问量统计
- 跳出率分析
- 页面访问数
- 平均访问时长
- 全球排名

### 3. 主要访问国家分布
- 圆环图展示国家分布
- 支持多个国家数据
- 颜色编码区分
- 百分比显示

### 4. 流量来源分析
- 直接访问
- 搜索引擎
- 社交媒体
- 外部引用
- 进度条可视化

### 5. 月度关键词价值分析
- 关键词价值排名
- 进度条展示相对价值
- 多彩色编码

## 技术实现

### 组件结构
```
src/components/TrafficAnalytics.tsx
├── ProgressBar - 进度条组件
├── DonutChart - 圆环图组件
├── AreaChart - 面积图组件
└── TrafficAnalytics - 主组件
```

### 样式设计
- 采用深色主题 (bg-gray-900, bg-gray-800)
- 使用 Tailwind CSS 进行样式管理
- 响应式网格布局 (lg:grid-cols-3)
- 平滑的过渡动画效果

### 数据结构
```typescript
interface TrafficData {
  monthlyTrend: Array<{month: string, visits: number}>;
  websiteStats: {
    visits: string;
    bounceRate: string;
    pageViews: string;
    avgDuration: string;
    globalRank: string;
  };
  topCountries: Array<{name: string, percent: number, color: string}>;
  trafficSources: Array<{name: string, percent: number, color: string}>;
  keywordValue: Array<{keyword: string, value: number, color: string}>;
}
```

## 集成方式

### 1. 在工具详情页面中集成
```tsx
import TrafficAnalytics from '@/components/TrafficAnalytics';

// 在工具介绍部分下方添加
<TrafficAnalytics toolName={translation.name || 'AI Tool'} />
```

### 2. 位置
- 位于工具详情页面的介绍部分下方
- 在功能特性展示之后
- 左侧主内容区域

## 设计特点

### 1. 视觉效果
- 深色主题与参考图片保持一致
- 使用蓝色系作为主色调
- 圆角设计增加现代感
- 适当的阴影效果

### 2. 用户体验
- 清晰的数据层次结构
- 直观的图表展示
- 响应式设计适配各种屏幕
- 平滑的动画过渡

### 3. 数据展示
- 多维度数据分析
- 图表与数字结合
- 颜色编码便于理解
- 百分比和绝对数值并存

## 未来扩展

### 1. 真实数据集成
- 连接真实的分析API
- 实时数据更新
- 历史数据对比

### 2. 交互功能
- 图表点击交互
- 时间范围选择
- 数据导出功能

### 3. 更多图表类型
- 柱状图
- 折线图
- 热力图
- 地图可视化

## 使用说明

1. 组件会自动使用mock数据进行展示
2. 传入工具名称作为标题显示
3. 支持深色和浅色主题切换
4. 响应式设计自动适配移动端

## 注意事项

- 当前使用mock数据，实际部署时需要连接真实数据源
- 图表组件使用SVG实现，确保浏览器兼容性
- 颜色方案可根据品牌需求调整
- 动画效果可通过CSS变量控制
