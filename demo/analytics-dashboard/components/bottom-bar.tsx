"use client"

import { Badge } from "@/components/ui/badge"
import { Server, Wifi, Database, Clock } from "lucide-react"

export function BottomBar() {
  const systemStats = [
    { label: "服务器状态", value: "正常", icon: Server, color: "green" },
    { label: "网络延迟", value: "12ms", icon: Wifi, color: "blue" },
    { label: "数据库", value: "活跃", icon: Database, color: "purple" },
    { label: "运行时间", value: "99.9%", icon: Clock, color: "orange" },
  ]

  return (
    <div className="bg-gradient-to-r from-slate-900/95 to-slate-800/95 backdrop-blur-sm border-t border-slate-700/50 px-6 py-3">
      <div className="flex items-center justify-between">
        {/* System Status */}
        <div className="flex items-center gap-6">
          {systemStats.map((stat) => {
            const Icon = stat.icon
            return (
              <div key={stat.label} className="flex items-center gap-2">
                <Icon className="w-4 h-4 text-slate-400" />
                <span className="text-xs text-slate-400">{stat.label}:</span>
                <Badge
                  variant="secondary"
                  className={`text-xs px-2 py-0.5 bg-${stat.color}-500/20 text-${stat.color}-400 border-${stat.color}-500/30`}
                >
                  {stat.value}
                </Badge>
              </div>
            )
          })}
        </div>

        {/* Copyright */}
        <div className="text-xs text-slate-500">© 2024 stakpak.dev - 数据分析平台 v2.1.0</div>
      </div>
    </div>
  )
}
