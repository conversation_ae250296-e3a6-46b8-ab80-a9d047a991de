"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Activity, Zap } from "lucide-react"
import { AnimatedCounter } from "./animated-counter"

export function SidebarStats() {
  const quickStats = [
    { label: "今日访问", value: 1247, trend: "+8.2%", up: true },
    { label: "在线用户", value: 89, trend: "+12", up: true },
    { label: "页面加载", value: 2.3, suffix: "s", trend: "-0.5s", up: false },
    { label: "转化率", value: 3.2, suffix: "%", trend: "+0.8%", up: true },
  ]

  const realtimeData = [
    { time: "14:30", visitors: 45, color: "#3B82F6" },
    { time: "14:35", visitors: 52, color: "#10B981" },
    { time: "14:40", visitors: 38, color: "#F59E0B" },
    { time: "14:45", visitors: 61, color: "#EF4444" },
    { time: "14:50", visitors: 47, color: "#8B5CF6" },
  ]

  return (
    <div className="space-y-4">
      {/* Real-time Status */}
      <Card className="bg-gradient-to-br from-slate-800/80 to-slate-900/80 border-slate-700/50 backdrop-blur-sm">
        <CardContent className="p-4">
          <div className="flex items-center gap-3 mb-4">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
              <span className="text-sm font-medium text-green-400">实时监控</span>
            </div>
            <Activity className="w-4 h-4 text-slate-400" />
          </div>

          <div className="space-y-3">
            {quickStats.map((stat, index) => (
              <div key={stat.label} className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-1 h-4 bg-blue-500 rounded-full" />
                  <span className="text-xs text-slate-300">{stat.label}</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-sm font-bold text-white">
                    <AnimatedCounter end={stat.value} suffix={stat.suffix || ""} />
                  </span>
                  <Badge
                    variant="secondary"
                    className={`text-xs px-1.5 py-0.5 ${
                      stat.up ? "bg-green-500/20 text-green-400" : "bg-red-500/20 text-red-400"
                    }`}
                  >
                    {stat.trend}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Mini Chart */}
      <Card className="bg-gradient-to-br from-slate-800/80 to-slate-900/80 border-slate-700/50 backdrop-blur-sm">
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-3">
            <span className="text-sm font-medium text-white">实时访问</span>
            <Zap className="w-4 h-4 text-yellow-400" />
          </div>

          <div className="h-20">
            <svg viewBox="0 0 200 60" className="w-full h-full">
              {realtimeData.map((data, index) => (
                <rect
                  key={data.time}
                  x={index * 35 + 10}
                  y={60 - data.visitors}
                  width="25"
                  height={data.visitors}
                  fill={data.color}
                  rx="2"
                  className="transition-all duration-500 hover:opacity-80"
                />
              ))}
            </svg>
          </div>

          <div className="flex justify-between text-xs text-slate-400 mt-2">
            {realtimeData.map((data) => (
              <span key={data.time}>{data.time}</span>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card className="bg-gradient-to-br from-slate-800/80 to-slate-900/80 border-slate-700/50 backdrop-blur-sm">
        <CardContent className="p-4">
          <h3 className="text-sm font-medium text-white mb-3">快速操作</h3>
          <div className="grid grid-cols-2 gap-2">
            <button className="p-2 bg-blue-500/20 hover:bg-blue-500/30 rounded-lg transition-colors text-xs text-blue-400 font-medium">
              导出报告
            </button>
            <button className="p-2 bg-green-500/20 hover:bg-green-500/30 rounded-lg transition-colors text-xs text-green-400 font-medium">
              刷新数据
            </button>
            <button className="p-2 bg-purple-500/20 hover:bg-purple-500/30 rounded-lg transition-colors text-xs text-purple-400 font-medium">
              设置提醒
            </button>
            <button className="p-2 bg-orange-500/20 hover:bg-orange-500/30 rounded-lg transition-colors text-xs text-orange-400 font-medium">
              查看详情
            </button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
