"use client"

import { Badge } from "@/components/ui/badge"
import { <PERSON>, Settings, Download, RefreshCw, Calendar } from "lucide-react"

export function TopBar() {
  return (
    <div className="bg-gradient-to-r from-slate-900/95 to-slate-800/95 backdrop-blur-sm border-b border-slate-700/50 px-6 py-4">
      <div className="flex items-center justify-between">
        {/* Left Section */}
        <div className="flex items-center gap-6">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
              <span className="text-white font-bold text-lg">S</span>
            </div>
            <div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
                stakpak.dev
              </h1>
              <p className="text-sm text-slate-400">数据分析仪表板</p>
            </div>
          </div>

          <div className="flex items-center gap-4">
            <Badge variant="secondary" className="bg-green-500/20 text-green-400 border-green-500/30">
              <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse" />
              实时更新
            </Badge>
            <Badge variant="secondary" className="bg-blue-500/20 text-blue-400 border-blue-500/30">
              最后更新: 2分钟前
            </Badge>
          </div>
        </div>

        {/* Right Section */}
        <div className="flex items-center gap-3">
          <button className="p-2.5 hover:bg-slate-700/50 rounded-lg transition-colors text-slate-400 hover:text-white">
            <Calendar className="w-5 h-5" />
          </button>
          <button className="p-2.5 hover:bg-slate-700/50 rounded-lg transition-colors text-slate-400 hover:text-white">
            <RefreshCw className="w-5 h-5" />
          </button>
          <button className="p-2.5 hover:bg-slate-700/50 rounded-lg transition-colors text-slate-400 hover:text-white">
            <Download className="w-5 h-5" />
          </button>
          <button className="p-2.5 hover:bg-slate-700/50 rounded-lg transition-colors text-slate-400 hover:text-white relative">
            <Bell className="w-5 h-5" />
            <div className="absolute -top-1 -right-1 w-2.5 h-2.5 bg-red-500 rounded-full" />
          </button>
          <button className="p-2.5 hover:bg-slate-700/50 rounded-lg transition-colors text-slate-400 hover:text-white">
            <Settings className="w-5 h-5" />
          </button>
        </div>
      </div>
    </div>
  )
}
