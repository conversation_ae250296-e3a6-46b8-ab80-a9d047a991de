"use client"

import type React from "react"

import { type ReactNode, useState } from "react"
import { Card } from "@/components/ui/card"

interface GlowingCardProps {
  children: ReactNode
  className?: string
  glowColor?: string
}

export function GlowingCard({ children, className = "", glowColor = "blue" }: GlowingCardProps) {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })
  const [isHovered, setIsHovered] = useState(false)

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    const rect = e.currentTarget.getBoundingClientRect()
    setMousePosition({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    })
  }

  const glowColors = {
    blue: "rgba(59, 130, 246, 0.4)",
    green: "rgba(16, 185, 129, 0.4)",
    purple: "rgba(139, 92, 246, 0.4)",
    pink: "rgba(236, 72, 153, 0.4)",
    orange: "rgba(245, 158, 11, 0.4)",
  }

  return (
    <div
      className="relative group"
      onMouseMove={handleMouseMove}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <Card
        className={`relative overflow-hidden bg-slate-800/50 border-slate-700 backdrop-blur-sm transition-all duration-500 hover:bg-slate-800/70 hover:border-slate-600 hover:shadow-2xl hover:shadow-blue-500/20 ${className}`}
        style={{
          transform: isHovered ? "translateY(-4px)" : "translateY(0px)",
        }}
      >
        {isHovered && (
          <div
            className="absolute pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-300"
            style={{
              background: `radial-gradient(200px circle at ${mousePosition.x}px ${mousePosition.y}px, ${glowColors[glowColor as keyof typeof glowColors]}, transparent 40%)`,
              left: 0,
              top: 0,
              right: 0,
              bottom: 0,
            }}
          />
        )}
        <div className="relative z-10">{children}</div>
      </Card>
    </div>
  )
}
