"use client"

interface PulseDotProps {
  color?: string
  size?: "sm" | "md" | "lg"
}

export function PulseDot({ color = "blue", size = "md" }: PulseDotProps) {
  const sizeClasses = {
    sm: "w-2 h-2",
    md: "w-3 h-3",
    lg: "w-4 h-4",
  }

  const colorClasses = {
    blue: "bg-blue-500",
    green: "bg-green-500",
    purple: "bg-purple-500",
    pink: "bg-pink-500",
    orange: "bg-orange-500",
  }

  return (
    <div className="relative flex items-center justify-center">
      <div
        className={`${sizeClasses[size]} ${colorClasses[color as keyof typeof colorClasses]} rounded-full animate-pulse`}
      />
      <div
        className={`absolute ${sizeClasses[size]} ${colorClasses[color as keyof typeof colorClasses]} rounded-full animate-ping opacity-75`}
      />
    </div>
  )
}
